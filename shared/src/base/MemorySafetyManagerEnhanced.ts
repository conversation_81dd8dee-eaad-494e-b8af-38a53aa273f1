/**
 * @file MemorySafetyManagerEnhanced
 * @filepath shared/src/base/MemorySafetyManagerEnhanced.ts
 * @task-id M-TSK-01.SUB-01.5.ENH-01
 * @component memory-safety-manager-enhanced
 * @reference foundation-context.MEMORY-SAFETY.006
 * @template on-demand-creation-with-latest-standards
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhancement
 * @created 2025-01-27
 * @modified 2025-01-27
 *
 * @description
 * Enhanced MemorySafetyManager with enterprise-grade component discovery, system coordination, and state management:
 * - Component discovery and auto-integration with compatibility validation
 * - Advanced system coordination patterns (groups, chains, resource sharing)
 * - System state management with capture, restore, and comparison capabilities
 * - Integration with all previous phases (AtomicCircularBuffer, EventHandler, Timer, Cleanup)
 * - 100% backward compatibility with existing MemorySafetyManager functionality
 * - Production-ready enhancements following Anti-Simplification Policy
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafetyManager
 * @integrates-with shared/src/base/AtomicCircularBufferEnhanced
 * @integrates-with shared/src/base/EventHandlerRegistryEnhanced
 * @integrates-with shared/src/base/TimerCoordinationServiceEnhanced
 * @integrates-with shared/src/base/CleanupCoordinatorEnhanced
 * @related-contexts foundation-context, memory-safety-context
 * @governance-impact framework-foundation, system-orchestration-enhancement
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-orchestrator-enhanced
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested, performance-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @backward-compatibility 100%
 * @anti-simplification-compliant true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-01-27) - Initial enhanced implementation with component discovery and system coordination
 * v1.1.0 (2025-07-29) - Completed modular refactoring: 1,398 → 316 lines (77% reduction) with 6 specialized modules
 * v1.2.0 (2025-07-29) - Enhanced component integration engine and system state management
 * v1.3.0 (2025-07-29) - Production-ready orchestrator with complete module delegation pattern
 * v1.4.0 (2025-07-30) - Integrated comprehensive resilient timing infrastructure with context-based measurements
 */

import { MemorySafetyManager, IMemorySafetyConfig, IMemorySafetyMetrics } from './MemorySafetyManager';
import { getEventHandlerRegistry } from './EventHandlerRegistry';
import { getEnhancedCleanupCoordinator } from './CleanupCoordinatorEnhanced';
import { getTimerCoordinator } from './TimerCoordinationService';

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import {
  ResilientTimer
} from './utils/ResilientTiming';

import {
  ResilientMetricsCollector
} from './utils/ResilientMetrics';

// Import all modular components
import { ComponentDiscoveryManager, IComponentDiscovery, IDiscoveredComponent, IMemorySafeComponent, IIntegrationResult, ICompatibilityResult, IRegisteredComponent } from './memory-safety-manager/modules/ComponentDiscoveryManager';
import { SystemCoordinationManager, ISystemCoordination, IComponentGroup, IGroupOperationResult, IResourceSharingGroup, IShutdownResult } from './memory-safety-manager/modules/SystemCoordinationManager';
import { EnhancedConfigurationManager, IEnhancedMemorySafetyConfig, IEnhancedMemorySafetyMetrics } from './memory-safety-manager/modules/EnhancedConfigurationManager';
import { ComponentIntegrationEngine, IComponentIntegrationEngine } from './memory-safety-manager/modules/ComponentIntegrationEngine';
import { SystemStateManager, ISystemStateManager, ISystemSnapshot, IStateRestoreResult } from './memory-safety-manager/modules/SystemStateManager';
import { EnhancedMetricsCollector, IEnhancedMetricsCollector, IMetricsSummary, ISystemHealthAssessment } from './memory-safety-manager/modules/EnhancedMetricsCollector';

// ============================================================================
// SECTION 1: ENHANCED ORCHESTRATOR INTERFACES (Lines 63-100)
// AI Context: "Main orchestrator interfaces for modular coordination"
// ============================================================================

/**
 * Enhanced Memory Safety Manager orchestrator interface
 */
export interface IMemorySafetyManagerEnhanced extends IComponentDiscovery, ISystemCoordination {
  // Enhanced configuration management
  validateAndNormalizeConfig(config: Partial<IEnhancedMemorySafetyConfig>): Promise<IEnhancedMemorySafetyConfig>;

  // Enhanced metrics and monitoring
  getEnhancedMetrics(): Promise<IEnhancedMemorySafetyMetrics>;
  getSystemHealthAssessment(): Promise<ISystemHealthAssessment>;
  getMetricsSummary(): Promise<IMetricsSummary>;

  // State management
  captureSystemSnapshot(name?: string): Promise<string>;
  restoreSystemSnapshot(snapshotId: string): Promise<IStateRestoreResult>;
  listSystemSnapshots(): Promise<Array<{ id: string; name: string; timestamp: Date }>>;
}


// ============================================================================
// SECTION 2: MAIN ENHANCED CLASS DECLARATION (Lines 86-150)
// AI Context: "Main MemorySafetyManagerEnhanced class with modular architecture"
// ============================================================================

/**
 * Enhanced Memory Safety Manager with modular architecture
 *
 * Orchestrates 6 specialized modules:
 * - ComponentDiscoveryManager: Component discovery and auto-integration
 * - SystemCoordinationManager: Component groups and coordination
 * - EnhancedConfigurationManager: Configuration management and validation
 * - ComponentIntegrationEngine: Integration logic and execution
 * - SystemStateManager: State capture and restoration
 * - EnhancedMetricsCollector: Metrics collection and monitoring
 */
export class MemorySafetyManagerEnhanced extends MemorySafetyManager implements IMemorySafetyManagerEnhanced {
  // Module instances
  private _componentDiscovery: ComponentDiscoveryManager;
  private _systemCoordination: SystemCoordinationManager;
  private _configurationManager: EnhancedConfigurationManager;
  private _integrationEngine: ComponentIntegrationEngine;
  private _stateManager: SystemStateManager;
  private _metricsCollector: EnhancedMetricsCollector;

  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern per prompt
  private _resilientTimer!: ResilientTimer;
  private _resilientMetricsCollector!: ResilientMetricsCollector;

  // Configuration
  private _enhancedConfig: IEnhancedMemorySafetyConfig;

  constructor(config: IEnhancedMemorySafetyConfig = {}) {
    super(config);

    // Initialize configuration manager first
    this._configurationManager = new EnhancedConfigurationManager();
    this._enhancedConfig = this._configurationManager.getDefaultConfig();

    // Initialize all modules
    this._componentDiscovery = new ComponentDiscoveryManager(config.discovery);
    this._systemCoordination = new SystemCoordinationManager();
    this._integrationEngine = new ComponentIntegrationEngine(this._componentDiscovery.getComponentRegistry());
    this._stateManager = new SystemStateManager(
      this._componentDiscovery.getComponentRegistry(),
      this._systemCoordination.getComponentGroups(),
      this._systemCoordination.getResourceSharingGroups(),
      config.stateManagement
    );
    this._metricsCollector = new EnhancedMetricsCollector();
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Initialize enhanced memory safety manager
   */
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    try {
      // RESILIENT TIMING INFRASTRUCTURE - Enterprise Configuration per prompt
      this._resilientTimer = new ResilientTimer({
        enableFallbacks: true,
        maxExpectedDuration: 30000, // 30 seconds max reasonable duration
        unreliableThreshold: 3, // 3 consecutive failures = unreliable
        estimateBaseline: 100 // 100ms baseline estimate for memory operations
      });

      this._resilientMetricsCollector = new ResilientMetricsCollector({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 300000, // 5 minutes
        defaultEstimates: new Map([
          ['component_discovery', 500],
          ['system_coordination', 300],
          ['configuration_validation', 200],
          ['integration_execution', 1000],
          ['state_management', 800],
          ['metrics_collection', 150]
        ])
      });

      this.logInfo('Resilient timing infrastructure initialized successfully', {
        timerFallbacksEnabled: true,
        metricsCollectionEnabled: true,
        performanceTarget: 'enterprise'
      });

      // Initialize all modules in dependency order
      await (this._configurationManager as any).initialize();
      await (this._componentDiscovery as any).initialize();
      await (this._systemCoordination as any).initialize();
      await (this._integrationEngine as any).initialize();
      await (this._stateManager as any).initialize();
      await (this._metricsCollector as any).initialize();

      this.logInfo('Enhanced Memory Safety Manager initialized with modular architecture and resilient timing');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Failed to initialize Enhanced Memory Safety Manager', { error: errorMessage });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Shutdown enhanced memory safety manager
   */
  protected async doShutdown(): Promise<void> {
    const shutdownContext = this._resilientTimer?.start();

    try {
      // Shutdown all modules in reverse dependency order
      await (this._metricsCollector as any).shutdown();
      await (this._stateManager as any).shutdown();
      await (this._integrationEngine as any).shutdown();
      await (this._systemCoordination as any).shutdown();
      await (this._componentDiscovery as any).shutdown();
      await (this._configurationManager as any).shutdown();

      await super.doShutdown();

      const shutdownTiming = shutdownContext?.end();
      if (shutdownTiming) {
        this._resilientMetricsCollector?.recordTiming('shutdown_operation', shutdownTiming);
      }

      this.logInfo('Enhanced Memory Safety Manager shutdown completed', {
        shutdownTime: shutdownTiming?.duration || 'unknown'
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Error during Enhanced Memory Safety Manager shutdown', { error: errorMessage });
      throw error;
    }
  }

  // ============================================================================
  // SECTION 3: COMPONENT DISCOVERY DELEGATION (Lines 151-200)
  // AI Context: "Component discovery methods delegated to ComponentDiscoveryManager"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Discover memory-safe components
   * RESILIENT TIMING INTEGRATION: Context-based timing for component discovery
   */
  async discoverMemorySafeComponents(): Promise<IDiscoveredComponent[]> {
    const timingContext = this._resilientTimer.start();

    try {
      const result = await this._componentDiscovery.discoverMemorySafeComponents();

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_discovery', timing);

      this.logInfo('Component discovery completed', {
        componentsFound: result.length,
        discoveryTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_discovery_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Component discovery failed', { error: errorMessage, discoveryTime: timing.duration });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Auto-integrate component
   * RESILIENT TIMING INTEGRATION: Context-based timing for component integration
   */
  async autoIntegrateComponent(component: IMemorySafeComponent): Promise<IIntegrationResult> {
    const timingContext = this._resilientTimer.start();

    try {
      const result = await this._componentDiscovery.autoIntegrateComponent(component);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_integration', timing);

      this.logInfo('Component integration completed', {
        componentId: component.id,
        integrationSuccess: result.success,
        integrationTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_integration_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Component integration failed', {
        componentId: component.id,
        error: errorMessage,
        integrationTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Validate component compatibility
   * RESILIENT TIMING INTEGRATION: Context-based timing for compatibility validation
   */
  validateComponentCompatibility(component: IMemorySafeComponent): ICompatibilityResult {
    const timingContext = this._resilientTimer.start();

    try {
      const result = this._componentDiscovery.validateComponentCompatibility(component);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('compatibility_validation', timing);

      this.logInfo('Component compatibility validation completed', {
        componentId: component.id,
        compatible: result.compatible,
        validationTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('compatibility_validation_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Component compatibility validation failed', {
        componentId: component.id,
        error: errorMessage,
        validationTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get component registry
   */
  getComponentRegistry(): Map<string, IRegisteredComponent> {
    return this._componentDiscovery.getComponentRegistry();
  }

  // ============================================================================
  // SECTION 4: SYSTEM COORDINATION DELEGATION (Lines 201-250)
  // AI Context: "System coordination methods delegated to SystemCoordinationManager"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Create component group
   */
  createComponentGroup(groupId: string, componentIds: string[]): IComponentGroup {
    return this._systemCoordination.createComponentGroup(groupId, componentIds);
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Coordinate group operation
   * RESILIENT TIMING INTEGRATION: Context-based timing for group operations
   */
  async coordinateGroupOperation(groupId: string, operation: string, parameters?: any): Promise<IGroupOperationResult> {
    const timingContext = this._resilientTimer.start();

    try {
      const result = await this._systemCoordination.coordinateGroupOperation(groupId, operation, parameters);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('group_operation_coordination', timing);

      this.logInfo('Group operation coordination completed', {
        groupId,
        operation,
        successfulComponents: result.successfulComponents,
        failedComponents: result.failedComponents,
        coordinationTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('group_operation_coordination_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Group operation coordination failed', {
        groupId,
        operation,
        error: errorMessage,
        coordinationTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Setup component chain
   * RESILIENT TIMING INTEGRATION: Context-based timing for chain setup
   */
  setupComponentChain(chain: any[]): string {
    const timingContext = this._resilientTimer.start();

    try {
      const chainId = this._systemCoordination.setupComponentChain(chain);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_chain_setup', timing);

      this.logInfo('Component chain setup completed', {
        chainId,
        chainLength: chain.length,
        setupTime: timing.duration
      });

      return chainId;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('component_chain_setup_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Component chain setup failed', {
        chainLength: chain.length,
        error: errorMessage,
        setupTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Create resource sharing group
   * RESILIENT TIMING INTEGRATION: Context-based timing for resource group creation
   */
  createResourceSharingGroup(groupId: string, resources: any[]): IResourceSharingGroup {
    const timingContext = this._resilientTimer.start();

    try {
      const group = this._systemCoordination.createResourceSharingGroup(groupId, resources);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('resource_sharing_group_creation', timing);

      this.logInfo('Resource sharing group created', {
        groupId,
        resourceCount: resources.length,
        creationTime: timing.duration
      });

      return group;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('resource_sharing_group_creation_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Resource sharing group creation failed', {
        groupId,
        resourceCount: resources.length,
        error: errorMessage,
        creationTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Orchestrate system shutdown
   * RESILIENT TIMING INTEGRATION: Context-based timing for system shutdown
   */
  async orchestrateSystemShutdown(strategy: 'graceful' | 'priority' | 'emergency'): Promise<IShutdownResult> {
    const timingContext = this._resilientTimer.start();

    try {
      const result = await this._systemCoordination.orchestrateSystemShutdown(strategy);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_shutdown_orchestration', timing);

      this.logInfo('System shutdown orchestration completed', {
        strategy,
        totalComponents: result.totalComponents,
        shutdownComponents: result.shutdownComponents,
        failedComponents: result.failedComponents,
        orchestrationTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_shutdown_orchestration_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('System shutdown orchestration failed', {
        strategy,
        error: errorMessage,
        orchestrationTime: timing.duration
      });
      throw error;
    }
  }

  // ============================================================================
  // SECTION 5: ENHANCED FUNCTIONALITY (Lines 251-300)
  // AI Context: "Enhanced functionality using all modules"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Validate and normalize configuration
   * RESILIENT TIMING INTEGRATION: Context-based timing for configuration validation
   */
  async validateAndNormalizeConfig(config: Partial<IEnhancedMemorySafetyConfig>): Promise<IEnhancedMemorySafetyConfig> {
    const timingContext = this._resilientTimer.start();

    try {
      const result = this._configurationManager.validateAndNormalizeConfig(config);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('configuration_validation', timing);

      if (result.valid) {
        this._enhancedConfig = result.normalizedConfig;

        this.logInfo('Configuration validation completed successfully', {
          validationTime: timing.duration,
          configurationValid: true
        });

        return result.normalizedConfig;
      } else {
        this.logError('Configuration validation failed', {
          validationTime: timing.duration,
          errors: result.errors
        });
        throw new Error(`Configuration validation failed: ${result.errors.join(', ')}`);
      }
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('configuration_validation_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Configuration validation error', {
        error: errorMessage,
        validationTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get enhanced metrics
   * RESILIENT TIMING INTEGRATION: Context-based timing for metrics collection
   */
  async getEnhancedMetrics(): Promise<IEnhancedMemorySafetyMetrics> {
    const timingContext = this._resilientTimer.start();

    try {
      const metrics = await this._metricsCollector.collectSystemMetrics();

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('enhanced_metrics_collection', timing);

      this.logInfo('Enhanced metrics collection completed', {
        collectionTime: timing.duration,
        metricsCount: Object.keys(metrics).length
      });

      return metrics;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('enhanced_metrics_collection_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Enhanced metrics collection failed', {
        error: errorMessage,
        collectionTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get system health assessment
   * RESILIENT TIMING INTEGRATION: Context-based timing for health assessment
   */
  async getSystemHealthAssessment(): Promise<ISystemHealthAssessment> {
    const timingContext = this._resilientTimer.start();

    try {
      const assessment = await this._metricsCollector.assessSystemHealth();

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_health_assessment', timing);

      this.logInfo('System health assessment completed', {
        assessmentTime: timing.duration,
        healthStatus: assessment.overallHealth
      });

      return assessment;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_health_assessment_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('System health assessment failed', {
        error: errorMessage,
        assessmentTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get metrics summary
   * RESILIENT TIMING INTEGRATION: Context-based timing for metrics summary
   */
  async getMetricsSummary(): Promise<IMetricsSummary> {
    const timingContext = this._resilientTimer.start();

    try {
      const summary = await this._metricsCollector.getMetricsSummary();

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('metrics_summary_generation', timing);

      this.logInfo('Metrics summary generation completed', {
        summaryTime: timing.duration
      });

      return summary;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('metrics_summary_generation_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('Metrics summary generation failed', {
        error: errorMessage,
        summaryTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Capture system snapshot
   * RESILIENT TIMING INTEGRATION: Context-based timing for snapshot creation
   */
  async captureSystemSnapshot(name?: string): Promise<string> {
    const timingContext = this._resilientTimer.start();

    try {
      const snapshotId = await this._stateManager.createSnapshot(name);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshot_capture', timing);

      this.logInfo('System snapshot captured successfully', {
        snapshotId,
        snapshotName: name || 'unnamed',
        captureTime: timing.duration
      });

      return snapshotId;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshot_capture_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('System snapshot capture failed', {
        snapshotName: name || 'unnamed',
        error: errorMessage,
        captureTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Restore system snapshot
   * RESILIENT TIMING INTEGRATION: Context-based timing for snapshot restoration
   */
  async restoreSystemSnapshot(snapshotId: string): Promise<IStateRestoreResult> {
    const timingContext = this._resilientTimer.start();

    try {
      const snapshot = this._stateManager.getSnapshot(snapshotId);
      if (!snapshot) {
        throw new Error(`Snapshot ${snapshotId} not found`);
      }

      const result = await this._stateManager.restoreSystemState(snapshot);

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshot_restore', timing);

      this.logInfo('System snapshot restored successfully', {
        snapshotId,
        restoreSuccess: result.success,
        restoreTime: timing.duration
      });

      return result;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshot_restore_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('System snapshot restore failed', {
        snapshotId,
        error: errorMessage,
        restoreTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: List system snapshots
   * RESILIENT TIMING INTEGRATION: Context-based timing for snapshot listing
   */
  async listSystemSnapshots(): Promise<Array<{ id: string; name: string; timestamp: Date }>> {
    const timingContext = this._resilientTimer.start();

    try {
      const snapshots = this._stateManager.listSnapshots().map(info => ({
        id: info.id,
        name: info.name,
        timestamp: info.timestamp
      }));

      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshots_listing', timing);

      this.logInfo('System snapshots listed successfully', {
        snapshotCount: snapshots.length,
        listingTime: timing.duration
      });

      return snapshots;
    } catch (error) {
      const timing = timingContext.end();
      this._resilientMetricsCollector.recordTiming('system_snapshots_listing_error', timing);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logError('System snapshots listing failed', {
        error: errorMessage,
        listingTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get enhanced configuration
   */
  getEnhancedConfiguration(): IEnhancedMemorySafetyConfig {
    return { ...this._enhancedConfig };
  }
}
