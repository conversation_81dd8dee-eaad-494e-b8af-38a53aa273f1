/**
 * @file Template Validation
 * @filepath shared/src/base/modules/cleanup/TemplateValidation.ts
 * @task-id M-TSK-01.SUB-02.1.MOD-09
 * @component template-validation
 * @reference foundation-context.CLEANUP-COORDINATION.010
 * @template modular-template-validation
 * @tier T1
 * @context cleanup-coordination-context
 * @category Cleanup-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Template validation module providing:
 * - Comprehensive validation system for cleanup templates
 * - Structure validation with schema compliance checking
 * - Dependency validation with circular dependency detection
 * - Condition evaluation with enterprise-grade quality checks
 * - ES6+ compliance with modern iteration patterns
 * - Jest compatibility with synchronous validation and async yielding
 * - Memory-safe validation operations without resource allocation
 * - Performance optimization with <1ms validation overhead per template
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-003-template-validation-architecture
 * @governance-dcr DCR-foundation-003-template-validation-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/modules/cleanup/TemplateDependencies
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager
 * @enables shared/src/base/CleanupCoordinatorEnhanced
 * @related-contexts cleanup-coordination-context, foundation-context
 * @governance-impact framework-foundation, cleanup-management, template-validation
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type cleanup-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/cleanup-coordination-context/modules/TemplateValidation.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial template validation implementation with comprehensive checks
 * v1.1.0 (2025-07-28) - Added structure and dependency validation with quality assurance
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IValidationResult,
  IValidationIssue,
  IStepCondition,
  IStepExecutionContext,
  IComponentRegistry
} from '../../types/CleanupTypes';
import { DependencyGraph, validateDependencyGraph } from './TemplateDependencies';

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import {
  ResilientTimer
} from '../../utils/ResilientTiming';

import {
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

/**
 * ============================================================================
 * AI CONTEXT: Comprehensive Template Validation System
 * Purpose: Validate template structure, dependencies, and execution conditions
 * Complexity: Moderate - Multi-layer validation with enterprise quality checks
 * AI Navigation: 5 logical sections - Structure, Dependencies, Conditions, Utilities, Export
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: CORE VALIDATION INTERFACES & TYPES (Lines 1-50)
 * AI Context: "Validation result types and configuration interfaces"
 * ============================================================================
 */

export interface ITemplateValidationConfig {
  strictMode: boolean;
  validateDependencies: boolean;
  validateConditions: boolean;
  validateParameters: boolean;
  maxOperationCount: number;
  maxDependencyDepth: number;
  allowedOperationTypes: string[];
}

export interface IExtendedValidationResult extends IValidationResult {
  performanceMetrics: {
    validationTime: number;
    checksPerformed: number;
    dependencyComplexity: number;
  };
  recommendations: string[];
  qualityScore: number;
}

/**
 * ============================================================================
 * SECTION 2: TEMPLATE STRUCTURE VALIDATION (Lines 51-200)
 * AI Context: "Basic template structure and format validation"
 * ============================================================================
 */

/**
 * Enhanced Template Validator
 *
 * Provides comprehensive template validation with enterprise-grade quality checks
 */
export class TemplateValidator extends MemorySafeResourceManager implements ILoggingService {
  private _logger: SimpleLogger;
  private _config: ITemplateValidationConfig;

  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern per prompt
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  constructor(config: Partial<ITemplateValidationConfig> = {}) {
    super({
      maxIntervals: 5,
      maxTimeouts: 10,
      maxCacheSize: 1 * 1024 * 1024, // 1MB for validation cache
      memoryThresholdMB: 100,
      cleanupIntervalMs: 300000
    });

    this._logger = new SimpleLogger('TemplateValidator');
    this._config = {
      strictMode: true,
      validateDependencies: true,
      validateConditions: true,
      validateParameters: true,
      maxOperationCount: 100,
      maxDependencyDepth: 20,
      allowedOperationTypes: [
        'cleanup',
        'validation',
        'preparation',
        'finalization',
        'rollback',
        'notification'
      ],
      ...config
    };
  }

  // Implement ILoggingService interface
  logInfo(message: string, metadata?: Record<string, any>): void {
    this._logger.logInfo(message, metadata);
  }

  logWarning(message: string, metadata?: Record<string, any>): void {
    this._logger.logWarning(message, metadata);
  }

  logError(message: string, error?: Error, metadata?: Record<string, any>): void {
    this._logger.logError(message, error, metadata);
  }

  logDebug(message: string, metadata?: Record<string, any>): void {
    this._logger.logDebug(message, metadata);
  }

  public async initialize(): Promise<void> {
    return this.doInitialize();
  }

  protected async doInitialize(): Promise<void> {
    this.logInfo('TemplateValidator initializing', {
      strictMode: this._config.strictMode,
      validateDependencies: this._config.validateDependencies
    });

    // RESILIENT TIMING INFRASTRUCTURE - Enterprise Configuration per prompt
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 10000, // 10 seconds for complex validation
      unreliableThreshold: 3, // 3 consecutive failures = unreliable
      estimateBaseline: 200 // 200ms baseline for validation operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['template_validation', 500],
        ['structure_validation', 200],
        ['dependency_validation', 300],
        ['condition_validation', 150],
        ['parameter_validation', 100]
      ])
    });

    this.logInfo('TemplateValidator resilient timing infrastructure initialized', {
      timerFallbacksEnabled: true,
      metricsCollectionEnabled: true,
      performanceTarget: 'enterprise'
    });
  }

  protected async doShutdown(): Promise<void> {
    this.logInfo('TemplateValidator shutting down');

    // RESILIENT TIMING INFRASTRUCTURE CLEANUP - Per prompt requirements
    try {
      if (this._metricsCollector) {
        // Get final metrics snapshot before shutdown
        const finalSnapshot = this._metricsCollector.createSnapshot();

        this.logInfo('TemplateValidator final resilient metrics snapshot', {
          totalMetrics: finalSnapshot.metrics.size,
          reliable: finalSnapshot.reliable,
          warnings: finalSnapshot.warnings.length
        });

        // Reset metrics collector
        this._metricsCollector.reset();
      }

      this.logInfo('TemplateValidator resilient timing infrastructure shutdown completed successfully');

    } catch (timingError) {
      this.logError('Error during TemplateValidator resilient timing infrastructure shutdown',
        timingError instanceof Error ? timingError : new Error(String(timingError)));
    }
  }

  /**
   * Comprehensive template validation
   */
  async validateTemplate(template: ICleanupTemplate): Promise<IExtendedValidationResult> {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const validationContext = this._resilientTimer.start();

    const startTime = performance.now();
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    this.logDebug('Starting template validation', {
      templateId: template.id,
      operationCount: template.operations.length,
      strictMode: this._config.strictMode
    });

    try {
      // LESSON LEARNED: Async yielding for Jest compatibility
      await Promise.resolve();

      // Basic structure validation
      const structureResult = await this._validateTemplateStructure(template);
      issues.push(...structureResult.issues);
      warnings.push(...structureResult.warnings);
      recommendations.push(...structureResult.recommendations);
      checksPerformed += structureResult.checksPerformed;

      // Operation validation
      const operationResult = await this._validateOperations(template.operations);
      issues.push(...operationResult.issues);
      warnings.push(...operationResult.warnings);
      recommendations.push(...operationResult.recommendations);
      checksPerformed += operationResult.checksPerformed;

      // Dependency validation
      if (this._config.validateDependencies) {
        const dependencyResult = await this._validateDependencies(template);
        issues.push(...dependencyResult.issues);
        warnings.push(...dependencyResult.warnings);
        recommendations.push(...dependencyResult.recommendations);
        checksPerformed += dependencyResult.checksPerformed;
      }

      // Condition validation
      if (this._config.validateConditions) {
        const conditionResult = await this._validateConditions(template.operations);
        issues.push(...conditionResult.issues);
        warnings.push(...conditionResult.warnings);
        recommendations.push(...conditionResult.recommendations);
        checksPerformed += conditionResult.checksPerformed;
      }

      // Parameter validation
      if (this._config.validateParameters) {
        const parameterResult = await this._validateParameters(template);
        issues.push(...parameterResult.issues);
        warnings.push(...parameterResult.warnings);
        recommendations.push(...parameterResult.recommendations);
        checksPerformed += parameterResult.checksPerformed;
      }

      const validationTime = performance.now() - startTime;
      const qualityScore = this._calculateQualityScore(template, issues, warnings);

      const result: IExtendedValidationResult = {
        valid: issues.filter(i => i.severity === 'error').length === 0,
        issues,
        warnings,
        suggestions: recommendations,
        performanceMetrics: {
          validationTime,
          checksPerformed,
          dependencyComplexity: this._calculateDependencyComplexity(template)
        },
        recommendations,
        qualityScore
      };

      this.logInfo('Template validation completed', {
        templateId: template.id,
        valid: result.valid,
        errorCount: issues.filter(i => i.severity === 'error').length,
        warningCount: warnings.length,
        qualityScore,
        validationTime
      });

      // Record successful validation timing
      const validationResult = validationContext.end();
      this._metricsCollector.recordTiming('template_validation', validationResult);

      return result;

    } catch (error) {
      // Record failed validation timing
      const validationResult = validationContext.end();
      this._metricsCollector.recordTiming('template_validation_failed', validationResult);

      const validationError = this._enhanceErrorContext(error instanceof Error ? error : new Error(String(error)), {
        context: 'template_validation',
        templateId: template.id,
        component: 'TemplateValidator'
      });

      this.logError('Template validation failed', validationError, {
        templateId: template.id,
        timingDuration: validationResult.duration
      });

      return {
        valid: false,
        issues: [{
          type: 'validation_error',
          message: `Validation failed: ${validationError.message}`,
          severity: 'error'
        }],
        warnings: [],
        suggestions: [],
        performanceMetrics: {
          validationTime: performance.now() - startTime,
          checksPerformed,
          dependencyComplexity: 0
        },
        recommendations: [],
        qualityScore: 0
      };
    }
  }

  /**
   * ============================================================================
   * SECTION 3: DEPENDENCY VALIDATION (Lines 201-350)
   * AI Context: "Template dependency validation and cycle detection"
   * ============================================================================
   */

  private async _validateDependencies(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    try {
      // Create dependency graph
      const graph = new DependencyGraph();
      
      // Add all operations as nodes
      template.operations.forEach(op => {
        graph.addNode(op.id);
        checksPerformed++;
      });

      // Add dependencies
      template.operations.forEach(op => {
        if (op.dependsOn && op.dependsOn.length > 0) {
          // Validate dependency references
          op.dependsOn.forEach(depId => {
            const dependencyExists = template.operations.some(o => o.id === depId);
            if (!dependencyExists) {
              issues.push({
                type: 'invalid_dependency',
                message: `Operation ${op.id} depends on non-existent operation ${depId}`,
                severity: 'error'
              });
            }
            checksPerformed++;
          });

          graph.addDependency(op.id, op.dependsOn);
        }
      });

      // Validate dependency graph
      const graphValidation = validateDependencyGraph(graph);
      
      if (!graphValidation.valid) {
        graphValidation.issues.forEach(issue => {
          issues.push({
            type: 'dependency_graph_error',
            message: issue,
            severity: 'error'
          });
        });
      }

      graphValidation.warnings.forEach(warning => {
        warnings.push(warning);
      });

      // Check dependency depth
      const criticalPath = graph.getCriticalPath();
      if (criticalPath.length > this._config.maxDependencyDepth) {
        warnings.push(`Dependency chain is deep (${criticalPath.length} steps). Consider parallel execution.`);
        recommendations.push('Consider breaking down complex dependencies into parallel groups');
      }

      // Check for parallel execution opportunities
      const parallelGroups = graph.getParallelGroups();
      if (parallelGroups.length > 1) {
        recommendations.push(`Template can be optimized with ${parallelGroups.length} parallel execution groups`);
      }

      checksPerformed += 5; // Graph validation checks

    } catch (error) {
      issues.push({
        type: 'dependency_validation_error',
        message: `Dependency validation error: ${error instanceof Error ? error.message : String(error)}`,
        severity: 'error'
      });
    }

    return { issues, warnings, recommendations, checksPerformed };
  }

  /**
   * ============================================================================
   * SECTION 4: CONDITION & PARAMETER VALIDATION (Lines 351-500)
   * AI Context: "Step condition evaluation and parameter validation"
   * ============================================================================
   */

  private async _validateConditions(operations: ICleanupTemplateStep[]): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // ES6+ COMPLIANT: Use forEach instead of for...of
    operations.forEach(operation => {
      if (operation.condition) {
        const conditionValid = this._validateStepCondition(operation.condition);
        if (!conditionValid.valid) {
          issues.push({
            type: 'invalid_condition',
            message: `Operation ${operation.id} has invalid condition: ${conditionValid.reason}`,
            severity: 'error'
          });
        }
        checksPerformed++;
      }

      // Validate condition logic
      if (operation.condition?.type === 'custom' && !operation.condition.customCondition) {
        issues.push({
          type: 'missing_condition_expression',
          message: `Operation ${operation.id} has custom condition but no customCondition function`,
          severity: 'error'
        });
      }

      // Check for component exists conditions
      if (operation.condition?.type === 'component_exists' && !operation.condition.componentId) {
        issues.push({
          type: 'missing_component_id',
          message: `Operation ${operation.id} has component_exists condition but no componentId`,
          severity: 'error'
        });
      }

      checksPerformed++;
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  private async _validateParameters(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // Validate template-level metadata
    if (template.metadata) {
      Object.entries(template.metadata).forEach(([key, value]) => {
        if (key.trim().length === 0) {
          warnings.push('Template metadata keys should not be empty');
        }

        if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
          warnings.push(`Template metadata '${key}' is an empty object`);
        }

        checksPerformed++;
      });
    }

    // Validate operation parameters
    template.operations.forEach(operation => {
      if (operation.parameters) {
        Object.entries(operation.parameters).forEach(([key, value]) => {
          if (key.trim().length === 0) {
            issues.push({
              type: 'invalid_parameter_name',
              message: `Operation ${operation.id} has empty parameter name`,
              severity: 'error'
            });
          }

          // Check for template references in parameters
          if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
            const templateRef = value.slice(2, -1);
            // For now, just warn about template references - could be expanded to validate against template metadata
            warnings.push(`Operation ${operation.id} parameter '${key}' uses template reference '${templateRef}'`);
          }

          checksPerformed++;
        });
      }
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  /**
   * ============================================================================
   * SECTION 5: VALIDATION UTILITIES (Lines 501-650)
   * AI Context: "Helper methods for structure validation and quality scoring"
   * ============================================================================
   */

  private async _validateTemplateStructure(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // Basic structure validation
    if (!template.id || template.id.trim().length === 0) {
      issues.push({
        type: 'missing_id',
        message: 'Template must have a valid ID',
        severity: 'error'
      });
    }
    checksPerformed++;

    if (!template.name || template.name.trim().length === 0) {
      warnings.push('Template should have a descriptive name');
      recommendations.push('Add a descriptive name for better template identification');
    }
    checksPerformed++;

    if (!template.description || template.description.trim().length === 0) {
      warnings.push('Template should have a description');
      recommendations.push('Add a description explaining the template purpose and usage');
    }
    checksPerformed++;

    if (!template.operations || template.operations.length === 0) {
      issues.push({
        type: 'no_operations',
        message: 'Template must contain at least one operation',
        severity: 'error'
      });
    }
    checksPerformed++;

    // Check operation count
    if (template.operations && template.operations.length > this._config.maxOperationCount) {
      warnings.push(`Template has ${template.operations.length} operations, consider breaking into smaller templates`);
      recommendations.push('Consider splitting large templates into smaller, focused templates');
    }
    checksPerformed++;

    // Validate version format
    if (template.version && !/^\d+\.\d+\.\d+/.test(template.version)) {
      warnings.push('Template version should follow semantic versioning (e.g., 1.0.0)');
    }
    checksPerformed++;

    return { issues, warnings, recommendations, checksPerformed };
  }

  private async _validateOperations(operations: ICleanupTemplateStep[]): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    const operationIds = new Set<string>();

    // ES6+ COMPLIANT: Use forEach instead of for...of
    operations.forEach(operation => {
      // Check for duplicate IDs
      if (operationIds.has(operation.id)) {
        issues.push({
          type: 'duplicate_operation_id',
          message: `Duplicate operation ID: ${operation.id}`,
          severity: 'error'
        });
      }
      operationIds.add(operation.id);
      checksPerformed++;

      // Validate operation structure
      if (!operation.id || operation.id.trim().length === 0) {
        issues.push({
          type: 'missing_operation_id',
          message: 'Operation must have a valid ID',
          severity: 'error'
        });
      }
      checksPerformed++;

      if (!operation.type || operation.type.trim().length === 0) {
        issues.push({
          type: 'missing_operation_type',
          message: `Operation ${operation.id} must have a type`,
          severity: 'error'
        });
      } else if (!this._config.allowedOperationTypes.includes(operation.type)) {
        warnings.push(`Operation ${operation.id} uses non-standard type: ${operation.type}`);
      }
      checksPerformed++;

      // Validate component pattern
      if (!operation.componentPattern || operation.componentPattern.trim().length === 0) {
        warnings.push(`Operation ${operation.id} should have a component pattern`);
      } else {
        try {
          new RegExp(operation.componentPattern);
        } catch (error) {
          issues.push({
            type: 'invalid_component_pattern',
            message: `Operation ${operation.id} has invalid regex pattern: ${operation.componentPattern}`,
            severity: 'error'
          });
        }
      }
      checksPerformed++;

      // Validate estimated duration
      if (operation.estimatedDuration <= 0) {
        warnings.push(`Operation ${operation.id} has invalid estimated duration: ${operation.estimatedDuration}`);
      }
      checksPerformed++;
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  private _validateStepCondition(condition: IStepCondition): { valid: boolean; reason?: string } {
    if (!condition.type) {
      return { valid: false, reason: 'Condition must have a type' };
    }

    const validTypes = ['always', 'never', 'on_success', 'on_failure', 'custom'];
    if (!validTypes.includes(condition.type)) {
      return { valid: false, reason: `Invalid condition type: ${condition.type}` };
    }

    if (condition.type === 'custom' && !condition.customCondition) {
      return { valid: false, reason: 'Custom condition must have a customCondition function' };
    }

    return { valid: true };
  }

  private _calculateDependencyComplexity(template: ICleanupTemplate): number {
    const graph = new DependencyGraph();
    
    template.operations.forEach(op => {
      graph.addNode(op.id);
      if (op.dependsOn && op.dependsOn.length > 0) {
        graph.addDependency(op.id, op.dependsOn);
      }
    });

    const metrics = graph.getGraphMetrics();
    return metrics.edgeCount + (metrics.maxDepth * 2) + (metrics.cycleCount * 10);
  }

  private _calculateQualityScore(
    template: ICleanupTemplate,
    issues: IValidationIssue[],
    warnings: string[]
  ): number {
    let score = 100;

    // Deduct for errors - more severe penalty
    const errors = issues.filter(i => i.severity === 'error');
    score -= errors.length * 25;

    // Deduct for warnings - increased penalty
    score -= warnings.length * 10;

    // Additional penalty for structural issues
    if (!template.description || template.description.trim().length === 0) {
      score -= 15; // Extra penalty for missing description
    }

    // Additional penalty for operations with missing IDs
    const operationsWithMissingIds = template.operations?.filter(op => !op.id || op.id.trim().length === 0) || [];
    score -= operationsWithMissingIds.length * 20;

    // Bonus for good practices (reduced to avoid inflating scores)
    if (template.description && template.description.length > 20) score += 3;
    if (template.version) score += 3;
    if (template.rollbackSteps && template.rollbackSteps.length > 0) score += 5;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Enhance error context with timing information
   */
  private _enhanceErrorContext(error: Error, context: {
    context: string;
    templateId?: string;
    component: string
  }): Error {
    const enhancedError = new Error(
      `${error.message} (Context: ${context.context}, Template: ${context.templateId || 'unknown'}, Component: ${context.component})`
    );
    enhancedError.name = error.name;
    enhancedError.stack = error.stack;
    return enhancedError;
  }
}

/**
 * ============================================================================
 * EXPORTED UTILITIES
 * ============================================================================
 */

/**
 * Quick template validation function
 */
export async function validateTemplate(
  template: ICleanupTemplate,
  config?: Partial<ITemplateValidationConfig>
): Promise<IExtendedValidationResult> {
  const validator = new TemplateValidator(config);
  return await validator.validateTemplate(template);
}

/**
 * Condition evaluation for step execution
 */
export function evaluateStepCondition(
  condition: IStepCondition,
  context: IStepExecutionContext
): boolean {
  switch (condition.type) {
    case 'always':
      return true;
    case 'on_success':
      return context.previousResults.size === 0 || 
             Array.from(context.previousResults.values()).every((result: any) => result.success);
    case 'on_failure':
      return Array.from(context.previousResults.values()).some((result: any) => !result.success);
    case 'custom':
      // Safe custom condition evaluation
      if (condition.customCondition) {
        try {
          return condition.customCondition(context);
        } catch (error) {
          return false; // Safe default on error
        }
      }
      return true;
    case 'component_exists':
      // Check if component exists in context
      return condition.componentId ? 
        context.globalContext.targetComponents.includes(condition.componentId) : 
        false;
    case 'resource_available':
      // Resource availability check - safe default for now
      return true;
    default:
      return true;
  }
}

/**
 * Component pattern matching utility
 */
export function findMatchingComponents(pattern: string, components: string[]): string[] {
  try {
    const regex = new RegExp(pattern, 'i');
    return components.filter(component => regex.test(component));
  } catch (error) {
    // Fallback to simple string matching if regex is invalid
    return components.filter(component => 
      component.toLowerCase().includes(pattern.toLowerCase())
    );
  }
} 